# Docker Compose Override for Development
# This file provides development-specific configurations
# Copy this to docker-compose.override.yml and modify as needed

version: '3.8'

# External network configuration
networks:
  interviewGPT_network:
    external: true

services:
  # Backend Service Configuration
  interviewGPT:
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=true
      - PYTHONUNBUFFERED=1
    env_file:
      - ../../app/backend/.env
    volumes:
      - ../../app/backend:/app
      - ../../uploads:/app/uploads
    restart: unless-stopped

  # Frontend Service Configuration
  react:
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    env_file:
      - ../../app/frontend/.env
    volumes:
      - ../../app/frontend/src:/app/src
      - ../../app/frontend/public:/app/public
    restart: unless-stopped
    depends_on:
      - interviewGPT
