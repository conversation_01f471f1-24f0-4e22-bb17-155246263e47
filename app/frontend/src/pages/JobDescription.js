import '../App.css';
import { useState, useEffect } from 'react';
import { Sidebar, Menu, MenuItem, SubMenu } from 'react-pro-sidebar';
import { v4 as uuidv4 } from 'uuid';
//import {DropdownMenu, DropdownToggle} from 'react-bootstrap';
import Dropdown from 'react-bootstrap/Dropdown';
import { Navbar, Container, Image, Form, FormControl, DropdownToggle, DropdownMenu} from 'react-bootstrap';

import DeletePresetModal from '../modals/DeletePresetModal.js';
import JobPresetEditForm from '../modals/JobPresetEditModal.js';
import ChangeStateModal from '../modals/ChangeStateModal.js';
// Side Bar Icons
import { DashboardIcon } from '../assets/icons/dashboard.tsx';
import { GenerateIcon } from '../assets/icons/generate.tsx';
import { PastInterviewIcon } from '../assets/icons/pastinterview.tsx';
import { JobDescriptionIcon} from '../assets/icons/jobdescription.tsx';
import { MembersIcon } from '../assets/icons/members.tsx';
import { SettingsIcon} from '../assets/icons/settings.tsx';
import { PlusIcon } from '../assets/icons/plusIcon.tsx';
import { getUserName, getUserImage } from '../index.js';

import { BackArrow } from '../assets/icons/backarrow.tsx';
import { Search } from '../assets/icons/search.tsx';
import { DDArrow } from '../assets/icons/dropdown_arrow.tsx';
import { validate_Date } from '../utils/utils.js';
import deleteImg from '../assets/delete.png';
import CreospanLogo from '../assets/creospanLogo.png';

import NotificationIcon from '../assets/NotificationIcon.png';

import { CollapseArrow } from '../assets/icons/collapsearrow.tsx';
import MandatoryQuestionModal from '../modals/MandQuestionModal.js';



const JobDescriptionPage = (args) =>{
    const userName = getUserName()
    const PowerAdmin = args.powerAdmin
    const backend_key = process.env.REACT_APP_BACKEND_API;
    const [ADDNEW, setADDNEW] = useState(true)
    const [FileCollection, setFileCollection] = useState(false)
    const [Archived, setArchived] = useState(false)
    const [Job_Title, setJobTitle] = useState(null)
    const [Job_Description, setJobDescription] = useState(null)
    const [ClientName, setClientName] = useState(null)
    const [ClientManager, setClientManager] = useState(null)
    const [QueryStr, SetQueryStr] = useState('');
    const [QueriedDB, SetQueriedDB] = useState(args.presetData);
    const [ArchiveDB, setArchiveDB] = useState(args.ARData)
    const [PageNum, SetPageNum] = useState(1);
    const [LastPageNum, SetLastPageNum] = useState(Archived ? Math.ceil((ArchiveDB.length || 1) / 10) : Math.ceil((QueriedDB.length || 1) / 10) )
    const [collapsed, setCollapsed] = useState(false)
    const [MandatoryQS, setMandatoryQS] = useState([])
    const [deleteUuid, setDeleteUuid] = useState();
    const [deleteDesc, setDeleteDesc] = useState();
    const [deleteJob, setDeleteJob] = useState();
    const [DetectPresetOpen, setPresetOpen] = useState(false)
    const [ChangeStateOpen, setChangeStateOpen] = useState(false)
    const [ChangeState, setChangeState] = useState(null)
    const [mandQModalOpened, setMandQModalOpened] = useState(false)
    const Profile = getUserImage()

    var bcolor = "#e08537"
    var addButtonColor = "grey"
    var fileButtonColor = "grey"
    var archiveButtonColor = "grey"

    const goBack = () =>{
        args.setJobDescPage(false)
        args.setViewDash(true)
     
    }

    useEffect(() => {
        if(args.presetData == null) {
            args.loadPresetsDB()
            SetQueriedDB(args.presetData)
        }
        if(JSON.stringify(args.presetData) != JSON.stringify(QueriedDB)) {
            args.loadPresetsDB()
            SetQueriedDB(args.presetData)
        }
        
    },[args.presetData, QueriedDB])

    useEffect(() => {
        if(args.ARData == null) {
            args.loadAR_DB()
            setArchiveDB(args.ARData)
        }
        if(JSON.stringify(args.ARData) != JSON.stringify(ArchiveDB)) {
            args.loadAR_DB()
            setArchiveDB(args.ARData)
        }
        
    },[args.ARData, ArchiveDB])
    
    
    if(ADDNEW){
        addButtonColor = "orange"
        fileButtonColor = "grey"
        archiveButtonColor ="grey"
    
    }
    else if(FileCollection){
        fileButtonColor = "orange"
        addButtonColor = "grey"
        archiveButtonColor ="grey"
    }
    else if(Archived){
        fileButtonColor = "grey"
        addButtonColor = "grey"
        archiveButtonColor ="orange"
      
    }
    else{
     addButtonColor = "orange"
    }
    
    if(!Job_Title || !Job_Description || !ClientManager || !ClientName || MandatoryQS.length == 0){
        bcolor = "gray"
    }
    else{
        bcolor = "#e08537"
    }

   
    
    const handleSavePreset = async () =>{
        console.log('Mandatory: ', MandatoryQS)
       
        if(Job_Title && Job_Description && ClientName && ClientManager && MandatoryQS?.length!=0 && MandatoryQS!=null){
           
           const date = await validate_Date()
           if(date){

            if(PowerAdmin==true){

            let saveData = {
                JD_Uuid : uuidv4(),
                Job_Title: Job_Title,
                Job_Description : Job_Description,
                date : date,
                creator: userName,
                Client_Name: ClientName,
                Client_Manager: ClientManager,
                Status: "Active",
                MandatoryQS: MandatoryQS

            };
        alert("Preset Created")
        await args.handleSavePreset(saveData)
        await args.loadPresetsDB()
        FileCollectionClicked()
        args.loadPresetsDB()
        }
        }
        }

    }
    function handleInputArray(index, field, value) {
        setMandatoryQS(prevState => {
            const newState = [...prevState];
                newState[index] = newState[index] || { question: "", lookFor: "", notes: "" };
    
            newState[index] = {
                ...newState[index], 
                [field]: value
            };
            console.log(newState)
            return newState;
        });
    }
   
    const handleStateChange = async (state, uuid) =>{
      
        const selectedPreset = await loadPreset(uuid)
           
        args.setSelectedPreset(selectedPreset)
        setChangeState(state)
        setChangeStateOpen(true)

    }
    

    const handleOpenEditor = async (uuid) =>{
        if(PowerAdmin==true && Archived==false){
            const selectedPreset = await loadPreset(uuid)
            args.setSelectedPreset(selectedPreset)
            console.log(selectedPreset)
        }
        setPresetOpen(true)
    }

    const loadPreset = async (PresetUuid) => {
        var route = ''
        if(Archived == false)
            route = `/getPreset`
        else
            route = `/getARPreset`
        return await new Promise((resolve, reject) => {
                
                args.setLoading(true);
                fetch(`${route}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'my-custom-header-key': backend_key,
                    },
                    credentials: 'include',
                    body: JSON.stringify({ uuid: PresetUuid }),
                })
                    .then(response => {
                        return response.json();
                    })
                    .then(data => {
                        if (!Archived){
                        args.setPresetData(data.JDData);
                        console.log(data.JDData);
                        args.setSelectedPreset(data.JDData)
                       
                        resolve(data.JDData || [])
                        }
                        else{
                            args.setARData(data.ARData);
                            console.log(data.ARData);
                            args.setSelectedPreset(data.ARData)

                            resolve(data.ARData || [])
                        }
                        args.setLoading(false);
                    })

            })
            .catch (error => {
                alert('Error retrieving Data');
                console.error(`Fetch error: ${error}`);
        })
    }

   
 
    function checkStatus(status)
    {
      
        if(status === "Active")
        {
            return '#8DBB59'
        }
        else if(status === 'Inactive')
            return '#F3BF42'

        else{
            return 'red'
        }
    }


    const getPageNum = (page) => {
        if(page === 'Previous'){
            if(PageNum > 1) {
                SetPageNum(PageNum - 1);
            }
        } else if(page === 'Next'){
            if(PageNum < LastPageNum){
                SetPageNum(PageNum + 1);
            }
        } else {
            SetPageNum(page);
        }
    }

    const pastInterviewsPage = () => {
        args.setNewInterview(false)
        args.setJobDescPage(false)
        args.loadDB((!args.viewDB), (args.viewDash))
    
    }

    const getDeleteInfo = (data) => {
        setDeleteUuid(data.uuid);
        setDeleteJob(data.Job_Title);
        setDeleteDesc(data.Job_Description);
    }

    const new_Interviews_Page = async () =>{
        args.setNewInterview(false)
        args.setJobDescPage(false)
        args.setNewInterview(true)
        await args.loadPresetsDB()
    } 

    const Dashboard_Page = () =>{
        goBack()
    }

    const ADDNEWClicked = () =>{
        setADDNEW(true)
        setFileCollection(false)
        setArchived(false)
        var addButton = document.getElementById("add")
        var fileButton = document.getElementById("file")
        addButton.style.color = "orange"
        fileButton.style.color = "grey" 
    }

    const FileCollectionClicked = async () =>{
        args.loadPresetsDB() 
        setFileCollection(true)
        setADDNEW(false)
        setArchived(false)
        console.log(PowerAdmin)
        await args.loadPresetsDB() 

       
    }
    const ArchiveClicked = async () =>{
       
        setArchived(true)
        setFileCollection(false)
        setADDNEW(false)
        archiveButtonColor ="orange"
        await args.loadAR_DB()

    }
    
    const handleQuery = (event) => {
        SetQueryStr(event.target.value);
        if(event.target.value.trim()) {
            SetQueriedDB(args.presetData.filter((item) => {
                if (item.Job_Title.toLowerCase().includes(event.target.value.trim().toLowerCase()) || item.Job_Description.toLowerCase().includes(event.target.value.trim().toLowerCase()) || item.Created_By.toLowerCase().includes(event.target.value.trim().toLowerCase())) {
                    return true;
                }
                return false;
            }));
        } else {
            SetQueriedDB(args.presetData);
        }
        SetPageNum(1);
        SetLastPageNum(Math.ceil((QueriedDB.length || 1) / 10));
    }

   

    /* Row Style for bakground colors */

    const rowColor = (status) => {
        
        
        if(status){
            return{
                
            cursor: "pointer",
          
            
            }
        }
            
       
    }

return (
    <>


      <Navbar className="navbar justify-content-between" style={{"height": "60px", padding:"0px"}}>  
                   
                        
                        <Navbar.Brand href="#" style={{display:'flex', alignItems:'center'}} > 
                        <Image src={CreospanLogo} roundedCircle width="60" height="60" />
                        </Navbar.Brand>
                       
                        
                      {/* <Container className='d-flex flex-row-reverse'>
                        <Form className='d-flex flex-row-reverse' style={{borderRadius:"50px"}}>
                           <FormControl type='search' id = "search" placeholder='🔍 Search' style={{display:'flex', alignItems:'center', backgroundColor:'#e49d60', border:'#e08537', paddingRight:"210px", color:'white', fontFamily:'Montserrat'}}></FormControl>
                        </Form>
                        </Container> */}
                      
                      
                    <Navbar.Brand href='#' style={{marginLeft:'86%'}}>
                        <Image src={NotificationIcon} roundedCircle width="30" height="30" ></Image>
                    </Navbar.Brand>
                    
                    <Navbar.Brand href ='#'>
                        <Image src={Profile} roundedCircle width="40" height="40"></Image>
                    </Navbar.Brand>
                     
        </Navbar>
    
        <div className='row main' style={{"marginLeft":'0px',"flex":"1","--bs-gutter-x":"0", overflow:'hidden'}}>

   
            <Sidebar collapsed ={collapsed} style={{ width: collapsed ? "50px" : "280px",'color':'orange', height:'100vh', display:'flex'}}>
                <button onClick={() => setCollapsed(!collapsed)} style={{paddingLeft:'1%', width: collapsed ? "100%" : "35%", border:"none", backgroundColor: "white"}}><CollapseArrow></CollapseArrow></button>       
                <Menu iconShape="square" style={{"margin-top":"40%", 'background-color':'white', border: 'white', fontFamily:'Montserrat' }}>
                    <MenuItem onClick={Dashboard_Page} icon={<DashboardIcon color='#E08537'/>} style={{paddingLeft:'1%'}}><div style={{marginLeft:'3%', marginTop:'5%'}}> Dashboard </div> </MenuItem>
                    <MenuItem onClick={new_Interviews_Page} icon={<GenerateIcon color='#E08537'/>} style={{paddingLeft:'3%', marginTop:'1%'}}> Create New </MenuItem>
                    <MenuItem onClick={pastInterviewsPage} icon={<PastInterviewIcon color='#E08537'/>} style={{paddingLeft:'3%'}}> Interviews </MenuItem>
                    <MenuItem onClick={JobDescriptionPage} icon={<JobDescriptionIcon color='#E08537'/>} style={{paddingLeft:'3%', marginTop:'2%'}}> Job Descriptions </MenuItem>
                    <MenuItem icon={<MembersIcon color='#E08537'/>} style={{paddingLeft:'3%', backgroundColor:'grey'}}> Members </MenuItem>
                    <MenuItem icon={<SettingsIcon color='#E08537'/>} style={{paddingLeft:'3%', backgroundColor:'grey'}}> Settings </MenuItem>
                </Menu>
            </Sidebar>


{  /*  <button className='fixed-top' style={{"margin": "30px 0 0 10px", "width":"80px", border:'none', backgroundColor:'#e08537'}} onClick={goBack}><BackArrow></BackArrow></button>
*/} <div className='main' style={{overflow: "initial", flex:1}}>
    <div className='w-100 file-upload-info d-flex css-dip3t8' style={{backgroundColor: 'white', height:'50px', marginBottom:'0px !important', paddingLeft:'3%'}} >
    <div>
    <button onClick={ADDNEWClicked} id='add' style={{fontSize:'13px',backgroundColor:'white', color: addButtonColor, border:'white', marginRight:'2%', height:'100%', width:'103%', fontFamily:'Montserrat'}}>Add New</button>
    </div>
    <div style={{marginLeft:'5%'}}>
    <button onClick={FileCollectionClicked} id='file' style={{color: fileButtonColor, fontSize:'13px',backgroundColor:'white', border:'white', marginRight:'2%', height:'100%', width:'100%', fontFamily:'Montserrat'}}>Job Descriptions</button>
    </div>
    
    { PowerAdmin && (
 
    <div style={{marginLeft:'5%'}}>
    <button onClick={ArchiveClicked} id='pending' style={{color: archiveButtonColor, fontSize:'13px',backgroundColor:'white', border:'white', marginRight:'2%', height:'100%', width:'100%', fontFamily:'Montserrat'}}>Archived</button>
    </div>
    
    )}

    <div className="input-group mx-auto" style={{marginBottom:'0px', fontSize:'12px', height:'30px', marginLeft:'30%', width:'20%', paddingTop:'6px'}}>
                 
        <input type="text" onChange={handleQuery} value={QueryStr} className="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" placeholder='🔍 Search'></input>
    </div>

    </div>

    { ADDNEW && (
        <>
    <main style={{display:'flex', marginTop:'4%'}}>
    <div className='side' style={{backgroundColor:'white', flex:1, marginLeft:'5%', fontFamily:'Montserrat', width:'50%'}}>
        

        <div style={{marginBottom:'2%'}}>
            <p style={{width:'100px', paddingLeft:'4%', color:'#5c5555'}}>Job Title:</p>
            <textarea className='JDTextArea' style={{width:'55%', height:'20%', marginLeft:'3%'} } value={Job_Title} onChange={(e) => setJobTitle(e.target.value)}></textarea>
        </div>

       

        <div style={{marginBottom:'2%'}}>
            <p style={{paddingLeft:'2.6%',  color:'#5c5555', fontFamily:'Montserrat'}}>Job Description:</p>
            <textarea className='JDTextArea' style={{marginLeft:'2.6%', width:'80%'}} value={Job_Description} onChange={(e) => setJobDescription(e.target.value)}></textarea>
        </div>

        
        <div style={{marginBottom:'2%'}}>
            <p style={{paddingLeft:'2.6%',  color:'#5c5555', fontFamily:'Montserrat'}}>Client Name:</p>
            <textarea className='JDTextArea' style={{marginLeft:'2.6%', width:'80%', height:'40px'}} value={ClientName} onChange={(e) => setClientName(e.target.value)}></textarea>
        </div>

        
        <div style={{marginBottom:'2%'}}>
            <p style={{paddingLeft:'2.6%',  color:'#5c5555', fontFamily:'Montserrat'}}>Client Manager:</p>
            <textarea className='JDTextArea' style={{marginLeft:'2.6%', width:'80%', height:'40px'}} value={ClientManager} onChange={(e) => setClientManager(e.target.value)}></textarea>
        </div>

        
    </div>

    <div className='side' style={{backgroundColor:'white', flex:1, marginLeft:'5%', fontFamily:'Montserrat', width:'50%'}}>
        

        <div style={{marginBottom:'2%'}}>
            <p style={{width:'100%', paddingLeft:'4%', color:'#5c5555'}}>Mandatory Interview Questions</p>
            <textarea className='JDTextArea' style={{width:'80%', marginLeft:'3%', marginBottom:'5%', height:'82px'} } value={MandatoryQS[0]?.question} placeholder='Question 1:' onChange={(e) => handleInputArray(0, "question", e.target.value)}></textarea>
            <textarea className='JDTextArea' style={{width:'80%', marginLeft:'3%', marginBottom:'5%', height:'82px'} } value={MandatoryQS[0]?.lookFor} placeholder='Look For:' onChange={(e) => handleInputArray(0, "lookFor", e.target.value)}></textarea>
            
        
            <div className='locked-add-question'>
                <textarea className='JDTextArea' style={{width:'80%', marginLeft:'3%', marginBottom:'5%', height:'82px'} } value={MandatoryQS[1]?.question} placeholder='Question 2:' onChange={(e) => handleInputArray(1, "question",e.target.value)}></textarea>
                <textarea className='JDTextArea' style={{width:'80%', marginLeft:'3%', marginBottom:'5%', height:'82px'} } value={MandatoryQS[1]?.lookFor} placeholder='Look For:' onChange={(e) => handleInputArray(1, "lookFor", e.target.value)}></textarea>
            
            <div className="locked-overlay-mand">
                <button className="btn btn-secondary p-3" style={{backgroundColor:'#e08537'}} data-bs-toggle="modal" data-bs-target="#mandQuestionModal" onClick={() => setMandQModalOpened(true)}>
                    <PlusIcon width="50px"/>
                </button>
            </div>
            </div>


        </div>
        </div>
    
    </main>
    <div style={{width:'13%', marginLeft:'42%', height:'100px'}}>
        <button style={{'background-color': bcolor, border: bcolor, borderRadius:'6px' }} type="button" className='uploadBtn' onClick={handleSavePreset}>Submit</button>
    </div>
    </>
    )}

    {FileCollection && (
        <div className='side' style={{backgroundColor:'white', flex:1, marginLeft:'5%'}}>
           

                <table className="table table-striped table-hover mx-auto" style={{overflow:'hidden', borderRadius:'6px', marginTop:'6%', width:'85%', fontFamily:'Montserrat', fontSize:'12px', fontWeight:'semibold'}}>
                <thead style={{borderBlockColor:'#D9D9D9', borderStyle:'solid', border:'solid #D9D9D9', borderInlineWidth:'1px', borderBlockWidth:'1px',fontFamily:'Montserrat'}}>
                    <tr>
                   
                    <th scope="col" style={{paddingLeft:'1.5%'}}>Job Title</th>
                    <th scope="col" style={{paddingLeft:'1.1%'}}>Date Uploaded</th>
                    <th scope="col" style={{paddingLeft:'2%'}}>Uploaded By</th>
                    <th scope="col" style={{paddingLeft:'2%'}}>Client Name</th>
                    <th scope="col" style={{paddingLeft:'2%'}}>Hiring Manager</th>
                    <th scope="col" style={{paddingLeft:'2%'}}>Status</th>
                    <th scope='col' style={{paddingLeft:'2%'}}> Delete</th>
                  
                   
                   </tr>
                </thead>
                <tbody>
                    {(QueriedDB.length ? (
                    QueriedDB.slice((PageNum-1)*10, 10*PageNum).map((data, num) => (
                        <tr key={num + 1} style={rowColor(data.Status)} >
                            
                            <td style={{padding:'20px'}} onClick={() => handleOpenEditor(data.uuid)}>{data.Job_Title}</td>
                            <td style={{padding:'20px'}} onClick={() => handleOpenEditor(data.uuid)}>{data.date}</td>
                            <td style={{padding:'20px'}} onClick={() => handleOpenEditor(data.uuid)}>{data.Created_By}</td>
                            <td style={{padding:'20px'}} onClick={() => handleOpenEditor(data.uuid)}>{data.Client_Name}</td>
                            <td style={{padding:'20px'}} onClick={() => handleOpenEditor(data.uuid)}>{data.Client_Manager}</td>
                           
                           
                            <td style={{width:'15%'}}>
                          
                          
                            <Dropdown style={{position:'absolute', zindex:'999'}}>
                            <Dropdown.Toggle variant="success"  style={{backgroundColor: checkStatus(data.Status), border:'none', color:'white', "position":'absolute'}} id="dropdown-basic">{data.Status} <DDArrow></DDArrow> </Dropdown.Toggle>

                                <Dropdown.Menu style={{'--bs-dropdown-padding-y':'0.0rem'}}>
                                    <Dropdown.Item style={{borderRadius:'6px', marginBottom:'2px', '--bs-dropdown-link-active-bg':'green'}} onClick={() => handleStateChange('Active', data.uuid)}>Set Active</Dropdown.Item>
                                    <Dropdown.Item style={{borderRadius:'6px', marginBottom:'2px', '--bs-dropdown-link-active-bg':'yellow'}} onClick={() => handleStateChange('Inactive', data.uuid)}>Set Inactive</Dropdown.Item>
                                    <Dropdown.Item style={{borderRadius:'6px', '--bs-dropdown-link-active-bg':'red'}} onClick={() => handleStateChange('Archived', data.uuid)}>Archive</Dropdown.Item>
                                </Dropdown.Menu>
                            </Dropdown>
                           
                            </td>


                          
                           
                            {/* include delete button only if admin user */}
                        { PowerAdmin && (
                            <>
                            <td style={{width:'16%'}}>
                                <button type="button" style={{paddingTop:'12px'}} className="btn " data-bs-toggle="modal" data-bs-target="#deletePresetModal" onClick={() => getDeleteInfo(data)}><img src={deleteImg} width='15px'></img></button>
                            </td>
                            </>
                        )}
                           
                           
                        </tr>
                    ))
                    ):(
                        QueryStr ? (
                            <div className='noResultsMessage'>
                                <h3>No Job Presets Found</h3>
                            </div>
                        ):
                        <div className='noResultsMessage'>
                            <h3>No Job Presets In Database</h3>
                        </div>
                    ))}
                </tbody>
                </table>
            
            {QueriedDB.length ? (
                <nav aria-label="Page navigation example" className='d-flex justify-content-center fixed-bottom mb-2' >
                    <ul className="pagination" >
                        <li className="page-item">
                            <button className="page-link" aria-label="Previous" onClick={() => getPageNum('Previous')} >
                                <span aria-hidden="true">&laquo;</span>
                            </button>
                        </li>
                        {PageNum > 1 ? (
                            <>
                                {PageNum > 2 ? (
                                    <>
                                        {PageNum > 3 ? (
                                            <>
                                                <li className="page-item">
                                                    <button className="page-link" onClick={() => getPageNum(1)}>1</button>
                                                </li>
                                                {PageNum > 4 ? (
                                                    <li className="page-item">
                                                        <button className="page-link">...</button>
                                                    </li>
                                                ): null}
                                            </>
                                        ): null}
                                        <li className="page-item">
                                            <button className="page-link" onClick={() => getPageNum(PageNum - 2)}>{PageNum - 2}</button>
                                        </li>
                                    </>
                                ):null}
                                <li className="page-item">
                                    <button className="page-link" onClick={() => getPageNum(PageNum - 1)}>{PageNum - 1}</button>
                                </li>
                            </>
                        ): null}
                        <li className="page-item active" aria-current="page">
                            <button className="page-link">{PageNum}</button>
                        </li>
                        {LastPageNum > PageNum ? (
                            <>
                                <li className="page-item">
                                    <button className="page-link" onClick={() => getPageNum(PageNum + 1)}>{PageNum + 1}</button>
                                </li>
                                {LastPageNum > (PageNum + 1) ? (
                                    <>
                                        <li className="page-item">
                                            <button className="page-link" onClick={() => getPageNum(PageNum + 2)}>{PageNum + 2}</button>
                                        </li>
                                        {LastPageNum > (PageNum + 2) ? (
                                            <>
                                                {LastPageNum > (PageNum + 3) ? (
                                                    <li className="page-item">
                                                        <button className="page-link">...</button>
                                                    </li>
                                                ):null}
                                                <li className="page-item">
                                                    <button className="page-link" onClick={() => getPageNum(LastPageNum)}>{LastPageNum}</button>
                                                </li>
                                            </>
                                        ): null}
                                    </>
                                ): null}
                            </>
                        ):null}
                        <li className="page-item">
                            <button className="page-link" aria-label="Next" onClick={() => getPageNum('Next')}>
                                <span aria-hidden="true">&raquo;</span>
                            </button>
                        </li>
                    </ul>
                </nav>
            ): null}
            
            </div>
    )}
    {Archived && (
          <div className='side' style={{backgroundColor:'white', flex:1, marginLeft:'5%', fontFamily:'Montserrat'}}>
              <h2 style={{marginBottom:'5%', fontWeight:'bold', marginLeft:'38%', marginTop:'2%'}}>Archived</h2>
            

                <table className="table table-striped table-hover mx-auto w-75">
                <thead>
                <tr>
                   
                   <th scope="col" style={{paddingLeft:'1.5%'}}>Job Title</th>
                   <th scope="col" style={{paddingLeft:'1.1%'}}>Date Uploaded</th>
                   <th scope="col" style={{paddingLeft:'2%'}}>Uploaded By</th>
                   <th scope="col" style={{paddingLeft:'2%'}}>Client Name</th>
                   <th scope="col" style={{paddingLeft:'2%'}}>Hiring Manager</th>
                   <th scope="col" style={{paddingLeft:'2%'}}>Status</th>
                   <th scope='col'> 
                  
               </th>
                 
                  
                  </tr>

                </thead>
                <tbody>
                    {(ArchiveDB.length ? (
                    ArchiveDB.slice((PageNum-1)*10, 10*PageNum).map((data, num) => (
                        <tr key={num + 1} style={{"cursor": "pointer"}}>
                            
                            <td onClick={() => ('')}>{data.Job_Title}</td>
                            <td onClick={() => ('')}>{data.date}</td>
                            <td onClick={() => ('')}>{data.Created_By}</td>
                            <td onClick={() => ('')}>{data.Client_Name}</td>
                            <td onClick={() => ('')}>{data.Client_Manager}</td>
                            <td>  
                            <Dropdown style={{position:'absolute', zindex:'999'}}>
                            <Dropdown.Toggle variant="success"  style={{backgroundColor: checkStatus(data.Status), border:'none', color:'white', "position":'fixed'}} id="dropdown-basic">{data.Status}</Dropdown.Toggle>
                            </Dropdown>
                            </td>
                            
                            <td style={{paddingLeft:'10%'}}>
                                <button type="button" style={{paddingTop:'12px'}} className="btn " data-bs-toggle="modal" data-bs-target="#deletePresetModal" onClick={() => getDeleteInfo(data)}><img src={deleteImg} width='15px'></img></button>
                            </td>

                          
                          
                          
                           
                        </tr>
                    ))
                    ):(
                        QueryStr ? (
                            <div className='noResultsMessage'>
                                <h3>No Job Presets Found</h3>
                            </div>
                        ):
                        <div className='noResultsMessage'>
                            <h3>No Job Presets In Database</h3>
                        </div>
                    ))}
                </tbody>
                </table>
            
            {ArchiveDB.length ? (
                <nav aria-label="Page navigation example" className='d-flex justify-content-center fixed-bottom mb-2' >
                    <ul className="pagination"  style={{marginLeft:'12% !important'}}>
                        <li className="page-item">
                            <button className="page-link" aria-label="Previous" onClick={() => getPageNum('Previous')} >
                                <span aria-hidden="true">&laquo;</span>
                            </button>
                        </li>
                        {PageNum > 1 ? (
                            <>
                                {PageNum > 2 ? (
                                    <>
                                        {PageNum > 3 ? (
                                            <>
                                                <li className="page-item">
                                                    <button className="page-link" onClick={() => getPageNum(1)}>1</button>
                                                </li>
                                                {PageNum > 4 ? (
                                                    <li className="page-item">
                                                        <button className="page-link">...</button>
                                                    </li>
                                                ): null}
                                            </>
                                        ): null}
                                        <li className="page-item">
                                            <button className="page-link" onClick={() => getPageNum(PageNum - 2)}>{PageNum - 2}</button>
                                        </li>
                                    </>
                                ):null}
                                <li className="page-item">
                                    <button className="page-link" onClick={() => getPageNum(PageNum - 1)}>{PageNum - 1}</button>
                                </li>
                            </>
                        ): null}
                        <li className="page-item active" aria-current="page">
                            <button className="page-link">{PageNum}</button>
                        </li>
                        {LastPageNum > PageNum ? (
                            <>
                                <li className="page-item">
                                    <button className="page-link" onClick={() => getPageNum(PageNum + 1)}>{PageNum + 1}</button>
                                </li>
                                {LastPageNum > (PageNum + 1) ? (
                                    <>
                                        <li className="page-item">
                                            <button className="page-link" onClick={() => getPageNum(PageNum + 2)}>{PageNum + 2}</button>
                                        </li>
                                        {LastPageNum > (PageNum + 2) ? (
                                            <>
                                                {LastPageNum > (PageNum + 3) ? (
                                                    <li className="page-item">
                                                        <button className="page-link">...</button>
                                                    </li>
                                                ):null}
                                                <li className="page-item">
                                                    <button className="page-link" onClick={() => getPageNum(LastPageNum)}>{LastPageNum}</button>
                                                </li>
                                            </>
                                        ): null}
                                    </>
                                ): null}
                            </>
                        ):null}
                        <li className="page-item">
                            <button className="page-link" aria-label="Next" onClick={() => getPageNum('Next')}>
                                <span aria-hidden="true">&raquo;</span>
                            </button>
                        </li>
                    </ul>
                </nav>
            ): null}
            
            </div>
    )
    }
   

    <DeletePresetModal deleteUuid={deleteUuid} deleteJob={deleteJob} deleteDesc={deleteDesc} loadPresetsDB={args.loadPresetsDB} loadPresetsAR={args.loadAR_DB} setLoading={args.setLoading} setLoadJD ={args.setLoadJD}  setViewDash={args.setViewDash} isPowerAdmin ={PowerAdmin} archiveClicked ={args.archiveClicked} />
    <JobPresetEditForm presetData ={args.selectedPreset} handleSavePreset ={args.handleSavePreset} setPresetOpen ={setPresetOpen} setJobDescPage = {args.setJobDescPage} isOpen ={DetectPresetOpen} loadPresetsDB={args.loadPresetsDB} setADDNEW ={setADDNEW} isPowerAdmin ={PowerAdmin} handleSaveUN ={args.handleSaveUN} loadUNPresets={args.loadUN_DB} UNData ={args.selectedPreset} PendingApprovals ={Archived} setSelectedPreset ={args.setSelectedPreset} setOpen ={setPresetOpen} />
    <ChangeStateModal newState = {ChangeState} handleSaveAR ={args.handleSaveAR} presetData ={args.selectedPreset} loadARPresets={args.loadAR_DB} handleSavePreset ={args.handleSavePreset} loadPresetsDB={args.loadPresetsDB} setJobDescPage = {args.setJobDescPage} setADDNEW ={setADDNEW} isOpen ={ChangeStateOpen} setIsOpen ={setChangeStateOpen} setSelectedPreset ={args.setSelectedPreset} archiveClicked = {Archived}/>
    <MandatoryQuestionModal isOpen ={mandQModalOpened} setIsOpen ={setMandQModalOpened} setSelectedPreset ={args.setSelectedPreset} presetData = {args.selectedPreset} handleSavePreset ={args.handleSavePreset} loadPresetsDB={args.loadPresetsDB} MandatoryQS = {MandatoryQS} setMandatoryQS ={setMandatoryQS}/>
   
    </div> 
   </div>
    
    
 
    
    </>
)}



export default JobDescriptionPage;